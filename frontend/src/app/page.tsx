"use client";
import { useState, useEffect } from "react";
import ReactMarkdown from "react-markdown";
import remarkGfm from "remark-gfm";
import rehypeRaw from "rehype-raw";

const AZ_PRIMARY = "#8A0051";
const AZ_ACCENT = "#EFAB00";
const AZ_BG = "#f8f6f9";

export default function Home() {
  const [username, setUsername] = useState("");
  const [password, setPassword] = useState("");
  const [token, setToken] = useState<string | null>(null);
  const [greeting, setGreeting] = useState("");
  const [error, setError] = useState("");
  const [menuOpen, setMenuOpen] = useState(false);
  const [showChangePwd, setShowChangePwd] = useState(false);
  const [selectedPub, setSelectedPub] = useState<string | null>(null);
  const [pubContent, setPubContent] = useState<string>("");
  const [publicationFiles, setPublicationFiles] = useState<string[]>([]);

  useEffect(() => {
    const savedToken = localStorage.getItem("token");
    if (savedToken) {
      setToken(savedToken);
      fetchMe(savedToken);
    }
  }, []);

  useEffect(() => {
    if (greeting) {
      fetch("http://localhost:8000/publications-list")
        .then((res) => res.json())
        .then(setPublicationFiles)
        .catch(() => setPublicationFiles([]));
    }
  }, [greeting]);

  useEffect(() => {
    if (selectedPub) {
      fetch(`http://localhost:8000/publication/${selectedPub}`)
        .then((res) => res.text())
        .then(setPubContent)
        .catch(() => setPubContent("Failed to load publication."));
    } else {
      setPubContent("");
    }
  }, [selectedPub]);

  const fetchMe = async (token: string) => {
    try {
      const res = await fetch("http://localhost:8000/me", {
        headers: { Authorization: token },
      });
      if (res.ok) {
        const data = await res.json();
        setGreeting(`Hello! ${data.username}`);
      } else {
        setGreeting("");
        setToken(null);
        localStorage.removeItem("token");
      }
    } catch {
      setGreeting("");
    }
  };

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    setError("");
    try {
      const res = await fetch("http://localhost:8000/login", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ username, password }),
      });
      if (res.ok) {
        const data = await res.json();
        setToken(data.token);
        localStorage.setItem("token", data.token);
        setGreeting(`Hello! ${data.username}`);
      } else {
        setError("Invalid credentials");
      }
    } catch {
      setError("Login failed");
    }
  };

  const handleLogout = () => {
    setToken(null);
    setGreeting("");
    localStorage.removeItem("token");
    setMenuOpen(false);
  };

  const handlePasswordChangeSuccess = () => {
    setShowChangePwd(false);
    setMenuOpen(false);
  };

  const MenuBar = () => (
    <div className="relative">
      <button
        className="p-2 rounded hover:bg-gray-100 flex items-center justify-center"
        onClick={() => setMenuOpen((v) => !v)}
        aria-label="Menu"
      >
        {/* Modern hamburger icon SVG */}
        <svg width="28" height="28" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <rect y="4" width="24" height="2.5" rx="1.25" fill={AZ_PRIMARY} />
          <rect y="10.75" width="24" height="2.5" rx="1.25" fill={AZ_PRIMARY} />
          <rect y="17.5" width="24" height="2.5" rx="1.25" fill={AZ_PRIMARY} />
        </svg>
      </button>
      {menuOpen && (
        <div className="absolute right-0 mt-2 w-48 bg-white border rounded shadow-lg z-10">
          <button
            className="block w-full text-left px-4 py-2 hover:bg-gray-100"
            onClick={() => {
              setShowChangePwd(true);
              setMenuOpen(false);
            }}
          >
            Change Password
          </button>
          <button
            className="block w-full text-left px-4 py-2 hover:bg-gray-100"
            onClick={handleLogout}
          >
            Logout
          </button>
        </div>
      )}
    </div>
  );

  const ChangePasswordModal = ({ onSuccess, onClose }: { onSuccess: () => void; onClose: () => void }) => {
    const [oldPwd, setOldPwd] = useState("");
    const [newPwd, setNewPwd] = useState("");
    const [changePwdMsg, setChangePwdMsg] = useState("");
    const canSubmit = oldPwd.length > 0 && newPwd.length > 0;

    const handleChangePassword = async (e: React.FormEvent) => {
      e.preventDefault();
      setChangePwdMsg("");
      try {
        const res = await fetch("http://localhost:8000/change-password", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: token || "",
          },
          body: JSON.stringify({ old_password: oldPwd, new_password: newPwd }),
        });
        if (res.ok) {
          setChangePwdMsg("Password changed successfully.");
          setTimeout(() => {
            onSuccess();
          }, 800);
        } else {
          const data = await res.json();
          setChangePwdMsg(data.detail || "Failed to change password.");
        }
      } catch {
        setChangePwdMsg("Failed to change password.");
      }
    };

    return (
      <div className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-30 z-20">
        <div className="bg-white p-6 rounded shadow-lg w-80 border" style={{ borderColor: AZ_PRIMARY }}>
          <h3 className="text-lg font-bold mb-4" style={{ color: AZ_PRIMARY }}>Change Password</h3>
          <form onSubmit={handleChangePassword} className="flex flex-col gap-3">
            <input
              type="password"
              placeholder="Old Password"
              value={oldPwd}
              onChange={(e) => setOldPwd(e.target.value)}
              className="border rounded px-3 py-2"
              style={{ borderColor: AZ_PRIMARY }}
              required
            />
            <input
              type="password"
              placeholder="New Password"
              value={newPwd}
              onChange={(e) => setNewPwd(e.target.value)}
              className="border rounded px-3 py-2"
              style={{ borderColor: AZ_PRIMARY }}
              required
            />
            {changePwdMsg && <div className="text-red-500 text-sm">{changePwdMsg}</div>}
            <div className="flex gap-2 mt-2">
              <button
                type="submit"
                className={`rounded px-4 py-2 font-semibold text-white ${canSubmit ? '' : 'opacity-50 cursor-not-allowed'}`}
                style={{ background: AZ_PRIMARY }}
                disabled={!canSubmit}
              >
                Change
              </button>
              <button
                type="button"
                className="rounded px-4 py-2 font-semibold border"
                style={{ borderColor: AZ_PRIMARY, color: AZ_PRIMARY }}
                onClick={onClose}
              >
                Cancel
              </button>
            </div>
          </form>
        </div>
      </div>
    );
  };

  if (greeting) {
    return (
      <div className="min-h-screen" style={{ background: AZ_BG }}>
        <div className="flex justify-end p-4">
          <MenuBar />
        </div>
        <div className="flex flex-col items-center justify-center min-h-[80vh] gap-8">
          <div className="bg-white p-8 rounded-lg shadow-lg text-center border w-full max-w-4xl" style={{ borderColor: AZ_PRIMARY }}>
            <h1 className="text-2xl font-bold mb-4" style={{ color: AZ_PRIMARY }}>{greeting}</h1>
            <div className="mb-4">
              <label className="block mb-2 font-semibold" style={{ color: AZ_PRIMARY }}>
                Select publication
              </label>
              <select
                className="border rounded px-3 py-2 w-64 focus:outline-none focus:ring-2"
                style={{ borderColor: AZ_PRIMARY }}
                value={selectedPub || ""}
                onChange={(e) => setSelectedPub(e.target.value || null)}
              >
                <option value="">-- Select --</option>
                {publicationFiles.map((file) => (
                  <option key={file} value={file}>
                    {file.replace(/_/g, " ")}
                  </option>
                ))}
              </select>
            </div>
            {pubContent && (
              <div className="prose prose-lg max-w-none text-left mt-6 mx-auto" style={{ color: 'inherit' }}>
                <ReactMarkdown remarkPlugins={[remarkGfm]} rehypePlugins={[rehypeRaw]}>{pubContent}</ReactMarkdown>
              </div>
            )}
          </div>
        </div>
        {showChangePwd && <ChangePasswordModal onSuccess={handlePasswordChangeSuccess} onClose={() => setShowChangePwd(false)} />}
      </div>
    );
  }

  return (
    <div className="flex items-center justify-center min-h-screen" style={{ background: AZ_BG }}>
      <form
        onSubmit={handleLogin}
        className="bg-white p-8 rounded-lg shadow-lg w-96 flex flex-col gap-4 border"
        style={{ borderColor: AZ_PRIMARY }}
      >
        <h2 className="text-2xl font-bold mb-4 text-center" style={{ color: AZ_PRIMARY }}>
          Antibody Drug Conjugates
        </h2>
        <input
          type="text"
          placeholder="Username"
          value={username}
          onChange={(e) => setUsername(e.target.value)}
          className="border rounded px-3 py-2 focus:outline-none focus:ring-2"
          style={{ borderColor: AZ_PRIMARY }}
          required
        />
        <input
          type="password"
          placeholder="Password"
          value={password}
          onChange={(e) => setPassword(e.target.value)}
          className="border rounded px-3 py-2 focus:outline-none focus:ring-2"
          style={{ borderColor: AZ_PRIMARY }}
          required
        />
        {error && <div className="text-red-500 text-sm">{error}</div>}
        <button
          type="submit"
          className="rounded px-4 py-2 font-semibold text-white"
          style={{ background: AZ_PRIMARY }}
        >
          Login
        </button>
      </form>
    </div>
  );
}
