from fastapi import <PERSON><PERSON><PERSON>, HTTPException, Depends, Request
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import json
import secrets
import os
from typing import Optional
from fastapi.responses import PlainTextResponse, JSONResponse
from pathlib import Path

app = FastAPI()

# Allow CORS for frontend
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


# In-memory token store for demo
active_tokens = {}

class LoginRequest(BaseModel):
    username: str
    password: str

class LoginResponse(BaseModel):
    token: str
    username: str

class ChangePasswordRequest(BaseModel):
    old_password: str
    new_password: str

PUBLICATIONS_DIR = Path(__file__).parent.parent / "data" / "publications"

@app.post("/login", response_model=LoginResponse)
def login(data: LoginRequest):
    # Load allowed users from JSON file
    users_path = os.path.join(os.path.dirname(__file__), '..', 'allowed_users.json')
    with open(users_path) as f:
        allowed_users = json.load(f)
    for user in allowed_users:
        if user["username"] == data.username and user["password"] == data.password:
            token = secrets.token_hex(16)
            active_tokens[token] = data.username
            return {"token": token, "username": data.username}
    raise HTTPException(status_code=401, detail="Invalid credentials")


def get_current_user(request: Request):
    token = request.headers.get("Authorization")
    if not token or token not in active_tokens:
        raise HTTPException(status_code=401, detail="Not authenticated")
    return active_tokens[token]

@app.get("/me")
def me(user: str = Depends(get_current_user)):
    return {"username": user}

@app.post("/change-password")
def change_password(data: ChangePasswordRequest, user: str = Depends(get_current_user)):
    # Load allowed users from JSON file
    users_path = os.path.join(os.path.dirname(__file__), '..', 'allowed_users.json')
    with open(users_path) as f:
        allowed_users = json.load(f)
    updated = False
    for u in allowed_users:
        if u["username"] == user and u["password"] == data.old_password:
            u["password"] = data.new_password
            updated = True
            break
    if not updated:
        raise HTTPException(status_code=400, detail="Old password is incorrect.")
    # Save updated users to file
    with open(users_path, "w") as f:
        json.dump(allowed_users, f, indent=2)
    return {"message": "Password changed successfully."}

@app.get("/publication/{pub_name}", response_class=PlainTextResponse)
def get_publication(pub_name: str):
    file_path = PUBLICATIONS_DIR / f"{pub_name}.md"
    if not file_path.exists():
        raise HTTPException(status_code=404, detail="File not found")
    return file_path.read_text(encoding="utf-8")

@app.get("/publications-list", response_class=JSONResponse)
def publications_list():
    files = []
    for f in PUBLICATIONS_DIR.glob("*.md"):
        files.append(f.stem)
    return files 